{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "button", "title": "<PERSON><PERSON>", "description": "A minimalistic tab component designed with React and Tailwind CSS.", "author": "<PERSON><PERSON><PERSON><PERSON>", "type": "registry:ui", "dependencies": [], "devDependencies": [], "registryDependencies": [], "cssVars": {"dark": {}, "light": {}}, "files": [{"path": "button.tsx", "content": "'use client'\n\nimport React, { useState, useEffect } from 'react';\n// A typed utility for combining class names. In a real project, you'd use a library like `tailwind-merge`.\nconst cn = (...inputs: (string | undefined | null | false)[]): string => {\n  return inputs.filter(Boolean).join(' ');\n}\n\n// SVG component for the loading spinner.\nconst Loader2: React.FC<{ className?: string }> = ({ className }) => (\n    <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"24\"\n        height=\"24\"\n        viewBox=\"0 0 24 24\"\n        fill=\"none\"\n        stroke=\"currentColor\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n        className={cn(\"animate-spin\", className)}\n    >\n        <path d=\"M21 12a9 9 0 1 1-6.219-8.56\" />\n    </svg>\n);\n\n// Define the types for variants and sizes explicitly\ntype ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\ntype ButtonSize = 'default' | 'sm' | 'lg';\n\n\n// Define the TypeScript interface for the Button's props.\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: ButtonVariant;\n  size?: ButtonSize;\n  loading?: boolean;\n}\n\n// Helper function to get the correct classes.\nconst getButtonClasses = ({ variant = 'default', size = 'default' }: Pick<ButtonProps, 'variant' | 'size'>) => {\n    const baseClasses = \"relative inline-flex items-center justify-center rounded-md text-sm font-medium transition-transform duration-75 focus:outline-none disabled:opacity-50 disabled:pointer-events-none overflow-hidden active:scale-[0.97]\";\n\n    const variantClasses = {\n        default: \"bg-slate-900 text-slate-50 hover:bg-slate-900/90 dark:bg-slate-50 dark:text-slate-900 dark:hover:bg-slate-50/90\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:text-white dark:hover:bg-red-700\",\n        outline: \"border border-slate-200 bg-transparent hover:bg-slate-100 text-slate-900 dark:border-slate-800 dark:hover:bg-slate-800 dark:text-slate-50\",\n        secondary: \"bg-slate-100 text-slate-900 hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-50 dark:hover:bg-slate-700\",\n        ghost: \"hover:bg-slate-100 text-slate-900 dark:hover:bg-slate-800 dark:text-slate-50\",\n        link: \"text-slate-900 underline-offset-4 hover:underline dark:text-slate-50\",\n    };\n\n    const sizeClasses = {\n        default: \"h-10 py-2 px-4\",\n        sm: \"h-9 px-3 rounded-md\",\n        lg: \"h-11 px-8 rounded-md\",\n    };\n\n    return cn(baseClasses, variantClasses[variant], sizeClasses[size]);\n};\n\n\n\ninterface Ripple {\n  x: number;\n  y: number;\n  size: number;\n  id: number;\n}\n\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, children, loading, onClick, ...props }, ref) => {\n    const [ripples, setRipples] = useState<Ripple[]>([]);\n    useEffect(() => {\n        const styleId = 'ripple-animation-style';\n        if (document.getElementById(styleId)) return;\n\n        const style = document.createElement('style');\n        style.id = styleId;\n        style.innerHTML = `\n            @keyframes ripple-effect {\n                from {\n                    transform: scale(0);\n                    opacity: 1;\n                }\n                to {\n                    transform: scale(2);\n                    opacity: 0;\n                }\n            }\n            .animate-ripple {\n                animation: ripple-effect 0.7s ease-out forwards;\n            }\n        `;\n        document.head.appendChild(style);\n    }, []);\n\n\n    const createRipple = (event: React.MouseEvent<HTMLButtonElement>) => {\n      if (loading) return;\n\n      const button = event.currentTarget;\n      const rect = button.getBoundingClientRect();\n      const rippleSize = Math.max(rect.width, rect.height);\n      const x = event.clientX - rect.left - rippleSize / 2;\n      const y = event.clientY - rect.top - rippleSize / 2;\n\n      const newRipple: Ripple = { x, y, size: rippleSize, id: Date.now() };\n\n      setRipples(currentRipples => [...currentRipples, newRipple]);\n      \n      setTimeout(() => {\n          setRipples(currentRipples => currentRipples.slice(1));\n      }, 700);\n\n      onClick?.(event);\n    };\n    \n    const rippleColor = (variant === 'default' || variant === 'destructive') \n      ? 'bg-white/30 dark:bg-slate-900/20' \n      : 'bg-slate-900/10 dark:bg-white/10';\n\n    return (\n      <button\n        className={cn(getButtonClasses({ variant, size }), className)}\n        onClick={createRipple}\n        disabled={loading}\n        ref={ref}\n        {...props}\n      >\n        <span className=\"relative z-10 flex items-center gap-2\">\n            {loading && <Loader2 className=\"h-4 w-4\" />}\n            {children}\n        </span>\n        \n        {!loading && (\n          <div className=\"absolute inset-0 z-0\">\n            {ripples.map((ripple) => (\n              <span\n                key={ripple.id}\n                className={cn(\"absolute rounded-full animate-ripple\", rippleColor)}\n                style={{ \n                    left: ripple.x, \n                    top: ripple.y, \n                    width: ripple.size, \n                    height: ripple.size \n                }}\n              />\n            ))}\n          </div>\n        )}\n      </button>\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport default Button", "type": "registry:ui"}]}